# -*- coding: utf-8 -*-
"""
DSSAD网络管理帧发送工具
用于发送唤醒帧和保持帧，确保DSSAD处于活跃状态
"""
import time
import threading
from typing import Optional

try:
    from uds_toolkit_lib_fixed import ZLGCANManager
    from dssad_config import get_config
except ImportError as e:
    print(f"导入错误: {e}")
    exit(1)

class NetworkManager:
    """网络管理器 - 负责发送网络管理帧"""
    
    def __init__(self, can_manager: ZLGCANManager):
        self.can = can_manager
        self.config = get_config()
        self.running = False
        self.use_canfd = self.config.get("can_settings.use_canfd", True)
        self.wakeup_thread: Optional[threading.Thread] = None
        self.usage_mode_thread: Optional[threading.Thread] = None
        
        # 从配置文件获取网络管理帧参数
        nm_config = self.config.get("network_management", {})
        
        # 唤醒帧配置
        wakeup_config = nm_config.get("wakeup_frame", {})
        self.wakeup_id = int(wakeup_config.get("can_id", "0x480"), 16)
        self.wakeup_data = bytes.fromhex(wakeup_config.get("data", "80 00 00 00 00 00 00 00").replace(" ", ""))
        self.wakeup_period = wakeup_config.get("period_ms", 500) / 1000.0
        
        # 保持帧配置
        usage_config = nm_config.get("usage_mode_frame", {})
        self.usage_id = int(usage_config.get("can_id", "0x1F1"), 16)
        self.usage_data = bytes.fromhex(usage_config.get("data", "00 00 00 20 2C 00 00 00").replace(" ", ""))
        self.usage_period = usage_config.get("period_ms", 20) / 1000.0
        
    def start(self):
        """开始发送网络管理帧"""
        if self.running:
            print("网络管理帧已在运行中")
            return
            
        self.running = True
        
        # 启动唤醒帧发送线程
        self.wakeup_thread = threading.Thread(target=self._wakeup_loop, daemon=True)
        self.wakeup_thread.start()
        
        # 启动保持帧发送线程
        self.usage_mode_thread = threading.Thread(target=self._usage_mode_loop, daemon=True)
        self.usage_mode_thread.start()
        
        print("网络管理帧发送已启动")
        print(f"唤醒帧: ID=0x{self.wakeup_id:03X}, 周期={self.wakeup_period*1000:.0f}ms")
        print(f"保持帧: ID=0x{self.usage_id:03X}, 周期={self.usage_period*1000:.0f}ms")
        
    def stop(self):
        """停止发送网络管理帧"""
        self.running = False
        
        if self.wakeup_thread:
            self.wakeup_thread.join(timeout=1)
        if self.usage_mode_thread:
            self.usage_mode_thread.join(timeout=1)
            
        print("网络管理帧发送已停止")
        
    def _wakeup_loop(self):
        """唤醒帧发送循环"""
        while self.running:
            try:
                self.can.send(self.wakeup_id, self.wakeup_data, use_canfd=self.use_canfd)
                time.sleep(self.wakeup_period)
            except Exception as e:
                print(f"发送唤醒帧失败: {e}")
                time.sleep(0.1)

    def _usage_mode_loop(self):
        """保持帧发送循环"""
        while self.running:
            try:
                self.can.send(self.usage_id, self.usage_data, use_canfd=self.use_canfd)
                time.sleep(self.usage_period)
            except Exception as e:
                print(f"发送保持帧失败: {e}")
                time.sleep(0.1)

def test_network_manager():
    """测试网络管理器"""
    print("DSSAD网络管理帧测试工具")
    print("=" * 50)
    
    config = get_config()
    
    # 创建CAN管理器
    can_manager = ZLGCANManager()
    
    # 连接设备
    device_type = config.get("can_settings.device_type", "USBCANFD-200U")
    device_idx = config.get("can_settings.device_index", 0)
    channel_idx = config.get("can_settings.channel_index", 0)
    
    print(f"连接CAN设备: {device_type}")
    if not can_manager.connect(device_type, device_idx, channel_idx):
        print("❌ CAN设备连接失败")
        return False
        
    print("✅ CAN设备连接成功")
    
    # 创建网络管理器
    nm = NetworkManager(can_manager)
    
    try:
        # 启动网络管理帧
        nm.start()
        
        print("\n网络管理帧正在发送...")
        print("请在另一个终端运行EOL测试工具")
        print("按 Ctrl+C 停止发送")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n收到停止信号")
    finally:
        nm.stop()
        can_manager.disconnect()
        print("程序已退出")

if __name__ == "__main__":
    test_network_manager()
