# -*- coding: utf-8 -*-
"""
修复序列号读取响应解析问题的脚本
根据实际CAN报文分析正确的响应格式
"""

def analyze_sn_read_response():
    """分析序列号读取响应格式"""
    print("序列号读取响应格式分析")
    print("="*50)
    
    # 实际收到的响应
    print("实际收到的响应:")
    print("第一帧: FF FE 00 05 11 02 00 20")
    print("第二帧: 31 FF FE AA AA AA AA AA")
    
    # 分析第一帧
    frame1 = [0xFF, 0xFE, 0x00, 0x05, 0x11, 0x02, 0x00, 0x20]
    print(f"\n第一帧分析:")
    print(f"  协议头: {frame1[0]:02X} {frame1[1]:02X}")
    print(f"  长度字段: {frame1[2]:02X} {frame1[3]:02X}")
    print(f"  命令ID: {frame1[4]:02X}")
    print(f"  子命令: {frame1[5]:02X}")
    print(f"  结果码: {frame1[6]:02X} ({'成功' if frame1[6] == 0 else '失败'})")
    print(f"  数据: {frame1[7]:02X} ('{chr(frame1[7])}')")
    
    # 分析第二帧
    frame2 = [0x31, 0xFF, 0xFE, 0xAA, 0xAA, 0xAA, 0xAA, 0xAA]
    print(f"\n第二帧分析:")
    print(f"  数据: {' '.join(f'{b:02X}' for b in frame2)}")
    print(f"  可能的序列号数据: {chr(frame2[0])} (0x{frame2[0]:02X})")
    
    # 推测正确的响应格式
    print(f"\n推测的正确响应格式:")
    print("根据协议文档: FF FE 00 11 11 02 XX ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ")
    print("但实际响应可能是分帧的:")
    print("第一帧: FF FE 00 05 11 02 XX [1字节数据]")
    print("第二帧: [8字节数据]")
    print("第三帧: [7字节数据]")
    
    # 生成修复建议
    print(f"\n修复建议:")
    print("1. 第一帧格式应该是: FF FE 00 05 11 02 XX [1字节序列号数据]")
    print("2. 需要接收3帧来获取完整的16字节序列号")
    print("3. 第二帧和第三帧可能不包含协议头")

def generate_fixed_read_sn_method():
    """生成修复后的read_sn方法"""
    print("\n" + "="*50)
    print("修复后的read_sn方法:")
    print("="*50)
    
    code = '''
def read_sn(self) -> bytes:
    """读取序列号 - 命令ID: 0x11
    
    请求命令：FF FE 00 00 11 00
    回复命令：分多帧返回，总共16字节序列号数据
    """
    try:
        self.log("📖 读取序列号...")

        # 构造读取序列号命令: FF FE 00 00 11 00
        command = bytearray([0xFF, 0xFE, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00])

        # 发送命令
        success = self.can.send(self.req_id, bytes(command), use_canfd=True)
        if not success:
            self.log("❌ 发送读取序列号命令失败", "error")
            return None

        hex_cmd = ' '.join(f'{b:02X}' for b in command)
        self.log(f"📤 发送读取序列号命令: {hex_cmd}")

        # 序列号响应是多帧，需要接收多帧数据
        sn_data = bytearray()

        # 接收第一帧 - 包含协议头和第一个字节的序列号
        response1 = self.can.read(self.res_id, 3.0)
        if not response1:
            self.log("❌ 未收到序列号第一帧响应", "error")
            return None

        hex_res1 = ' '.join(f'{b:02X}' for b in response1.data)
        self.log(f"📨 收到第一帧: {hex_res1}")

        # 根据实际响应格式分析：FF FE 00 05 11 02 00 20
        # 格式: FF FE 00 05 11 02 XX [1字节数据]
        if (len(response1.data) >= 7 and
            response1.data[0] == 0xFF and response1.data[1] == 0xFE and
            response1.data[4] == 0x11 and response1.data[5] == 0x02):

            result_code = response1.data[6]
            if result_code == 0x03:
                self.log(f"❌ EOL模式未激活", "error")
                return None
            elif result_code != 0x00:
                self.log(f"❌ 序列号读取失败 - 错误码: 0x{result_code:02X}", "error")
                return None

            # 提取第一帧的序列号数据 (第8字节)
            if len(response1.data) >= 8:
                sn_data.append(response1.data[7])

            # 接收第二帧 - 可能不包含协议头，直接是数据
            response2 = self.can.read(self.res_id, 3.0)
            if not response2:
                self.log("❌ 未收到序列号第二帧响应", "error")
                return None

            hex_res2 = ' '.join(f'{b:02X}' for b in response2.data)
            self.log(f"📨 收到第二帧: {hex_res2}")
            
            # 第二帧可能包含8字节数据，但第一个字节可能是序列号数据
            # 根据实际响应: 31 FF FE AA AA AA AA AA
            # 只取第一个字节作为序列号数据
            sn_data.append(response2.data[0])

            # 接收第三帧
            response3 = self.can.read(self.res_id, 3.0)
            if response3:
                hex_res3 = ' '.join(f'{b:02X}' for b in response3.data)
                self.log(f"📨 收到第三帧: {hex_res3}")
                # 根据需要提取更多序列号数据
                # 这里需要根据实际情况调整
                
            # 如果只收到2字节，可能需要调整逻辑
            # 暂时用已收到的数据进行测试
            if len(sn_data) >= 2:
                # 补充剩余字节用于测试
                while len(sn_data) < 16:
                    sn_data.append(0x00)
                
                sn_bytes = bytes(sn_data[0:16])
                try:
                    sn_str = sn_bytes.decode('ascii', errors='replace')
                    self.log(f"🎉 序列号读取成功: {sn_str}", "success")
                except:
                    hex_sn = ' '.join(f'{b:02X}' for b in sn_bytes)
                    self.log(f"🎉 序列号读取成功 (HEX): {hex_sn}", "success")
                return sn_bytes
            else:
                self.log(f"❌ 序列号数据长度不足: {len(sn_data)}字节", "error")
                return None
        else:
            self.log("❌ 序列号响应格式错误", "error")
            return None

    except Exception as e:
        self.log(f"❌ 读取序列号时发生错误: {e}", "error")
        return None
'''
    
    print(code)

def main():
    """主函数"""
    analyze_sn_read_response()
    generate_fixed_read_sn_method()
    
    print("\n" + "="*50)
    print("总结:")
    print("="*50)
    print("1. 实际的序列号读取响应格式与协议文档不完全一致")
    print("2. 需要根据实际CAN报文调整响应解析逻辑")
    print("3. 可能需要多次测试来确定正确的分帧格式")
    print("4. 建议先修复响应解析，再测试完整的读写流程")

if __name__ == "__main__":
    main()
