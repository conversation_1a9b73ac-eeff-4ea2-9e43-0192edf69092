# -*- coding: utf-8 -*-
"""
DSSAD EOL测试工具GUI界面
"""
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from typing import Optional
from datetime import datetime
try:
    from uds_toolkit_lib_fixed import ZLGCANManager, DEVICE_TYPES, DSADEOLTester
    from network_manager import NetworkManager
except ImportError as e:
    messagebox.showerror("导入错误", f"无法导入必要的模块: {e}")
    exit()

class DSSADEOLApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("DSSAD EOL测试工具")
        self.geometry("1000x800")
        self.configure(bg="#F0F0F0")
        
        # 设备管理
        self.can_manager: Optional[ZLGCANManager] = None
        self.eol_tester: Optional[DSADEOLTester] = None
        self.network_manager: Optional[NetworkManager] = None
        self.is_connected = False
        
        # 测试数据
        self.test_results = []
        
        # 创建界面
        self._create_widgets()
        self._set_ui_state(False)
        
        # 绑定关闭事件
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def _create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 连接设置区域
        conn_frame = ttk.LabelFrame(main_frame, text=" CAN设备连接 ", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 设备设置行
        device_row = ttk.Frame(conn_frame)
        device_row.pack(fill=tk.X, pady=5)
        
        ttk.Label(device_row, text="设备类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.device_type_var = tk.StringVar(value=list(DEVICE_TYPES.keys())[0])
        self.device_combo = ttk.Combobox(device_row, textvariable=self.device_type_var, 
                                        values=list(DEVICE_TYPES.keys()), state="readonly", width=15)
        self.device_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(device_row, text="设备索引:").pack(side=tk.LEFT, padx=(10, 5))
        self.device_idx_var = tk.StringVar(value="0")
        self.device_idx_entry = ttk.Entry(device_row, textvariable=self.device_idx_var, width=5)
        self.device_idx_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(device_row, text="通道索引:").pack(side=tk.LEFT, padx=(10, 5))
        self.channel_idx_var = tk.StringVar(value="0")
        self.channel_idx_entry = ttk.Entry(device_row, textvariable=self.channel_idx_var, width=5)
        self.channel_idx_entry.pack(side=tk.LEFT, padx=5)
        
        # 连接按钮
        self.connect_btn = ttk.Button(device_row, text="连接", command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=(20, 0))

        # 网络管理按钮
        self.nm_btn = ttk.Button(device_row, text="启动网络管理", command=self.toggle_network_manager)
        self.nm_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # 测试控制区域
        test_frame = ttk.LabelFrame(main_frame, text=" EOL测试控制 ", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 测试按钮行
        btn_row1 = ttk.Frame(test_frame)
        btn_row1.pack(fill=tk.X, pady=5)
        
        self.key_injection_ext_btn = ttk.Button(btn_row1, text="密钥灌装(Extended)", command=lambda: self.run_key_injection(0x01))
        self.key_injection_ext_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.key_injection_prog_btn = ttk.Button(btn_row1, text="密钥灌装(Programming)", command=lambda: self.run_key_injection(0x02))
        self.key_injection_prog_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.security_btn = ttk.Button(btn_row1, text="安全访问", command=self.run_security_access)
        self.security_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.enter_eol_btn = ttk.Button(btn_row1, text="进入EOL模式", command=self.enter_eol_mode)
        self.enter_eol_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.exit_eol_btn = ttk.Button(btn_row1, text="退出EOL模式", command=self.exit_eol_mode)
        self.exit_eol_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.full_test_btn = ttk.Button(btn_row1, text="完整测试", command=self.run_full_test)
        self.full_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 第二行测试按钮
        btn_row2 = ttk.Frame(test_frame)
        btn_row2.pack(fill=tk.X, pady=5)
        
        self.read_hw_btn = ttk.Button(btn_row2, text="读取硬件版本", command=self.read_hw_version)
        self.read_hw_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.read_sw_btn = ttk.Button(btn_row2, text="读取软件版本", command=self.read_sw_version)
        self.read_sw_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.read_sn_btn = ttk.Button(btn_row2, text="读取序列号", command=self.read_sn)
        self.read_sn_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.ig_test_btn = ttk.Button(btn_row2, text="IG电平测试", command=self.test_ig_level)
        self.ig_test_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 数据写入区域
        write_frame = ttk.LabelFrame(main_frame, text=" 数据写入 ", padding="10")
        write_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 序列号写入
        sn_row = ttk.Frame(write_frame)
        sn_row.pack(fill=tk.X, pady=2)
        
        ttk.Label(sn_row, text="序列号:").pack(side=tk.LEFT, padx=(0, 5))
        self.sn_var = tk.StringVar(value=" EXAMPLE_SN_12345")  # 16字节
        self.sn_entry = ttk.Entry(sn_row, textvariable=self.sn_var, width=20)
        self.sn_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        
        ttk.Button(sn_row, text="写入SN", command=self.write_sn).pack(side=tk.LEFT, padx=5)
        
        # 生产日期写入
        date_row = ttk.Frame(write_frame)
        date_row.pack(fill=tk.X, pady=2)
        
        ttk.Label(date_row, text="生产日期:").pack(side=tk.LEFT, padx=(0, 5))
        self.date_var = tk.StringVar(value=datetime.now().strftime("%y%m%d"))  # YYMMDD格式
        self.date_entry = ttk.Entry(date_row, textvariable=self.date_var, width=10)
        self.date_entry.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(date_row, text="写入日期", command=self.write_date).pack(side=tk.LEFT, padx=5)
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(main_frame, text=" 状态信息 ", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        status_row = ttk.Frame(status_frame)
        status_row.pack(fill=tk.X)
        
        ttk.Label(status_row, text="连接状态:").pack(side=tk.LEFT)
        self.conn_status_label = ttk.Label(status_row, text="未连接", foreground="red")
        self.conn_status_label.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(status_row, text="EOL模式:").pack(side=tk.LEFT)
        self.eol_status_label = ttk.Label(status_row, text="未激活", foreground="red")
        self.eol_status_label.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(status_row, text="安全访问:").pack(side=tk.LEFT)
        self.security_status_label = ttk.Label(status_row, text="未解锁", foreground="red")
        self.security_status_label.pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(status_row, text="网络管理:").pack(side=tk.LEFT)
        self.nm_status_label = ttk.Label(status_row, text="未启动", foreground="red")
        self.nm_status_label.pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text=" 测试日志 ", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        # 日志控制按钮
        log_ctrl_frame = ttk.Frame(log_frame)
        log_ctrl_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(log_ctrl_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT)
        ttk.Button(log_ctrl_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(log_ctrl_frame, text="生成报告", command=self.generate_report).pack(side=tk.LEFT, padx=(10, 0))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        # 配置日志颜色
        self.log_text.tag_config("info", foreground="#000000")
        self.log_text.tag_config("success", foreground="#008000", font=("", 9, "bold"))
        self.log_text.tag_config("error", foreground="#FF0000")
        self.log_text.tag_config("warning", foreground="#FF8000")
        
    def log(self, message: str, tag: str = "info"):
        """添加日志消息"""
        def _update_log():
            self.log_text.config(state=tk.NORMAL)
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.insert(tk.END, f"[{timestamp}] {message}\n", tag)
            self.log_text.config(state=tk.DISABLED)
            self.log_text.see(tk.END)
        
        self.after(0, _update_log)
        
    def _set_ui_state(self, connected: bool):
        """设置UI状态"""
        self.is_connected = connected
        
        # 连接相关控件
        state = tk.DISABLED if connected else tk.NORMAL
        self.device_combo.config(state=tk.DISABLED if connected else "readonly")
        self.device_idx_entry.config(state=state)
        self.channel_idx_entry.config(state=state)
        
        # 测试相关控件
        test_state = tk.NORMAL if connected else tk.DISABLED
        for widget in [self.key_injection_ext_btn, self.key_injection_prog_btn, self.security_btn,
                      self.enter_eol_btn, self.exit_eol_btn, self.full_test_btn, self.read_hw_btn,
                      self.read_sw_btn, self.read_sn_btn, self.ig_test_btn, self.sn_entry,
                      self.date_entry]:
            widget.config(state=test_state)
            
        # 更新连接按钮
        if connected:
            self.connect_btn.config(text="断开")
            self.conn_status_label.config(text="已连接", foreground="green")
        else:
            self.connect_btn.config(text="连接")
            self.conn_status_label.config(text="未连接", foreground="red")
            self.eol_status_label.config(text="未激活", foreground="red")
            self.security_status_label.config(text="未解锁", foreground="red")
            self.nm_status_label.config(text="未启动", foreground="red")

    def toggle_connection(self):
        """切换连接状态"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """连接CAN设备"""
        try:
            device_type = self.device_type_var.get()
            device_idx = int(self.device_idx_var.get())
            channel_idx = int(self.channel_idx_var.get())
        except ValueError:
            messagebox.showerror("参数错误", "设备索引和通道索引必须是数字")
            return

        def _connect_worker():
            self.log("正在连接CAN设备...")
            self.can_manager = ZLGCANManager()
            success = self.can_manager.connect(device_type, device_idx, channel_idx)

            def _update_ui():
                if success:
                    self.eol_tester = DSADEOLTester(self.can_manager, self.config)
                    self.network_manager = NetworkManager(self.can_manager)
                    self._set_ui_state(True)
                    self.log("CAN设备连接成功", "success")
                    self.log("提示: 请先启动网络管理帧，然后再进行EOL测试", "warning")
                else:
                    self.log("CAN设备连接失败", "error")
                    messagebox.showerror("连接失败", "无法连接到CAN设备")

            self.after(0, _update_ui)

        threading.Thread(target=_connect_worker, daemon=True).start()

    def disconnect(self):
        """断开CAN设备"""
        # 先停止网络管理
        if self.network_manager:
            self.network_manager.stop()
            self.network_manager = None

        if self.can_manager:
            self.can_manager.disconnect()
            self.can_manager = None
            self.eol_tester = None
            self._set_ui_state(False)
            self.log("CAN设备已断开", "info")

    def run_security_access(self):
        """执行安全访问"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            success = self.eol_tester.pyt()
            def _update_ui():
                if success:
                    self.security_status_label.config(text="已解锁", foreground="green")
                    self.log("安全访问成功", "success")
                else:
                    self.security_status_label.config(text="未解锁", foreground="red")
                    self.log("安全访问失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def run_key_injection(self, security_level: int):
        """执行密钥灌装

        Args:
            security_level: 安全级别 (0x01=Extended, 0x02=Programming)
        """
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        level_name = "Extended level" if security_level == 0x01 else "Programming level"

        def _worker():
            success = self.eol_tester.security_key_injection(security_level)
            def _update_ui():
                if success:
                    self.log(f"{level_name}密钥灌装成功", "success")
                else:
                    self.log(f"{level_name}密钥灌装失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def enter_eol_mode(self):
        """进入EOL模式"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            success = self.eol_tester.enter_eol_mode()
            def _update_ui():
                if success:
                    self.eol_status_label.config(text="已激活", foreground="green")
                    self.log("进入EOL模式成功", "success")
                else:
                    self.eol_status_label.config(text="未激活", foreground="red")
                    self.log("进入EOL模式失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def exit_eol_mode(self):
        """退出EOL模式"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            success = self.eol_tester.exit_eol_mode()
            def _update_ui():
                if success:
                    self.eol_status_label.config(text="未激活", foreground="red")
                    self.log("退出EOL模式成功", "success")
                else:
                    self.log("退出EOL模式失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def toggle_network_manager(self):
        """切换网络管理状态"""
        if not self.network_manager:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        if hasattr(self.network_manager, 'running') and self.network_manager.running:
            # 停止网络管理
            self.network_manager.stop()
            self.nm_btn.config(text="启动网络管理")
            self.nm_status_label.config(text="未启动", foreground="red")
            self.log("网络管理帧已停止", "info")
        else:
            # 启动网络管理
            self.network_manager.start()
            self.nm_btn.config(text="停止网络管理")
            self.nm_status_label.config(text="运行中", foreground="green")
            self.log("网络管理帧已启动", "success")
            self.log("正在发送唤醒帧(0x480)和保持帧(0x1F1)", "info")

    def run_full_test(self):
        """运行完整测试"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            self.log("开始运行完整EOL测试流程...", "info")
            success = self.eol_tester.run_complete_eol_test()
            def _update_ui():
                if success:
                    self.log("完整EOL测试流程完成", "success")
                    messagebox.showinfo("测试完成", "EOL测试流程已完成，请查看日志了解详细结果")
                else:
                    self.log("完整EOL测试流程失败", "error")
                    messagebox.showerror("测试失败", "EOL测试流程失败，请查看日志了解详细信息")

                # 更新状态标签
                if self.eol_tester.eol_mode_active:
                    self.eol_status_label.config(text="已激活", foreground="green")
                else:
                    self.eol_status_label.config(text="未激活", foreground="red")

                if self.eol_tester.security_unlocked:
                    self.security_status_label.config(text="已解锁", foreground="green")
                else:
                    self.security_status_label.config(text="未解锁", foreground="red")

            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def read_hw_version(self):
        """读取硬件版本"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            hw_version = self.eol_tester.read_internal_hw_version()
            def _update_ui():
                if hw_version is not None:
                    self.log(f"硬件版本读取成功: 0x{hw_version:02X}", "success")
                else:
                    self.log("硬件版本读取失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def read_sw_version(self):
        """读取软件版本"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            sw_version = self.eol_tester.read_sw_version()
            def _update_ui():
                if sw_version:
                    hex_str = ' '.join(f'{b:02X}' for b in sw_version)
                    self.log(f"软件版本读取成功: {hex_str}", "success")
                else:
                    self.log("软件版本读取失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def read_sn(self):
        """读取序列号"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            sn_data = self.eol_tester.read_sn()
            def _update_ui():
                if sn_data:
                    try:
                        sn_str = sn_data.decode('ascii', errors='replace')
                        self.log(f"序列号读取成功: {sn_str}", "success")
                    except:
                        hex_str = ' '.join(f'{b:02X}' for b in sn_data)
                        self.log(f"序列号读取成功 (HEX): {hex_str}", "success")
                else:
                    self.log("序列号读取失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def test_ig_level(self):
        """测试IG电平"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        def _worker():
            ig_level = self.eol_tester.read_ig_level()
            def _update_ui():
                if ig_level is not None:
                    level_str = "High" if ig_level else "Low"
                    self.log(f"IG电平读取成功: {level_str}", "success")
                else:
                    self.log("IG电平读取失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def write_sn(self):
        """写入序列号"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        sn_str = self.sn_var.get()
        if len(sn_str) != 16:
            messagebox.showerror("参数错误", "序列号必须是16个字符")
            return

        def _worker():
            sn_data = sn_str.encode('ascii')
            success = self.eol_tester.write_sn(sn_data)
            def _update_ui():
                if success:
                    self.log(f"序列号写入成功: {sn_str}", "success")
                else:
                    self.log("序列号写入失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def write_date(self):
        """写入生产日期"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        date_str = self.date_var.get()
        if len(date_str) != 6:
            messagebox.showerror("参数错误", "生产日期必须是6位数字 (YYMMDD)")
            return

        try:
            # 转换为BCD格式
            date_data = self.eol_tester.string_to_bcd(date_str)
        except ValueError:
            messagebox.showerror("参数错误", "生产日期格式错误，必须是数字")
            return

        def _worker():
            success = self.eol_tester.write_manufacture_date(date_data)
            def _update_ui():
                if success:
                    self.log(f"生产日期写入成功: {date_str}", "success")
                else:
                    self.log("生产日期写入失败", "error")
            self.after(0, _update_ui)

        threading.Thread(target=_worker, daemon=True).start()

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete('1.0', tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_log(self):
        """保存日志"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="保存日志文件"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    log_content = self.log_text.get('1.0', tk.END)
                    f.write(log_content)
                messagebox.showinfo("保存成功", f"日志已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存日志时发生错误: {e}")

    def generate_report(self):
        """生成测试报告"""
        if not self.eol_tester:
            messagebox.showwarning("警告", "请先连接CAN设备")
            return

        report = self.eol_tester.generate_test_report()

        # 显示报告窗口
        report_window = tk.Toplevel(self)
        report_window.title("EOL测试报告")
        report_window.geometry("600x500")

        report_text = scrolledtext.ScrolledText(report_window, wrap=tk.WORD)
        report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        report_text.insert('1.0', report)
        report_text.config(state=tk.DISABLED)

        # 保存报告按钮
        save_btn = ttk.Button(report_window, text="保存报告",
                             command=lambda: self._save_report(report))
        save_btn.pack(pady=5)

    def _save_report(self, report_content: str):
        """保存测试报告"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
            title="保存测试报告"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                messagebox.showinfo("保存成功", f"测试报告已保存到: {filename}")
            except Exception as e:


                messagebox.showerror("保存失败", f"保存报告时发生错误: {e}")

    def on_closing(self):
        """关闭程序"""
        if self.is_connected:
            if messagebox.askokcancel("退出", "设备仍在连接中，确定要断开并退出吗？"):
                self.disconnect()
                self.destroy()
        else:
            self.destroy()

if __name__ == "__main__":
    app = DSSADEOLApp()
    app.mainloop()
