@echo off
chcp 65001 >nul
echo ========================================
echo DSSAD EOL测试问题快速修复工具
echo ========================================
echo.
echo 根据您的问题分析，发现了关键信息：
echo 1. 【关键】DSSAD只响应CANFD模式，不响应普通CAN模式
echo 2. 缺少网络管理帧导致DSSAD休眠
echo 3. 需要先进行安全访问密钥灌装（Extended + Programming级别）
echo 4. 密钥灌装必须在安全访问之前完成
echo.
echo 解决方案：
echo 1. 使用更新后的GUI工具（已支持CANFD+密钥灌装，推荐）
echo 2. 使用诊断工具排查问题
echo 3. 手动启动网络管理帧
echo.
echo 请选择操作：
echo 1. 启动带网络管理的GUI工具
echo 2. 运行诊断工具
echo 3. 仅启动网络管理帧
echo 4. 查看故障排查指南
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5): 

if "%choice%"=="1" (
    echo.
    echo 正在启动带网络管理功能的GUI工具...
    echo 使用说明：
    echo 1. 连接CAN设备（确保使用CANFD模式）
    echo 2. 点击"启动网络管理"按钮 ^<-- 重要！
    echo 3. 等待10秒让DSSAD唤醒
    echo 4. 点击"密钥灌装(Extended)"按钮
    echo 5. 点击"密钥灌装(Programming)"按钮
    echo 6. 再点击"安全访问"
    echo 注意：工具已自动配置为CANFD模式
    echo.
    pause
    python dssad_eol_gui.py
) else if "%choice%"=="2" (
    echo.
    echo 正在启动诊断工具...
    echo 诊断工具将自动检测问题并给出建议
    echo.
    pause
    python dssad_diagnostic.py
) else if "%choice%"=="3" (
    echo.
    echo 正在启动网络管理帧发送工具...
    echo 此工具将持续发送网络管理帧
    echo 请在另一个窗口运行EOL测试工具
    echo.
    pause
    python network_manager.py
) else if "%choice%"=="4" (
    echo.
    echo 正在打开故障排查指南...
    start 故障排查指南.md
) else if "%choice%"=="5" (
    echo 退出程序
    exit
) else (
    echo 无效选择，请重新运行
    pause
)

pause
