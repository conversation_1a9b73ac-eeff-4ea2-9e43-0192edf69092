# -*- coding: utf-8 -*-
"""
检查代码语法和导入
"""

def check_imports():
    """检查主要模块的导入"""
    try:
        print("检查 uds_toolkit_lib_fixed.py...")
        from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
        print("✅ uds_toolkit_lib_fixed 导入成功")
        
        print("检查 dssad_config.py...")
        from dssad_config import DSSADConfig
        print("✅ dssad_config 导入成功")
        
        print("检查 dssad_eol_gui.py...")
        import dssad_eol_gui
        print("✅ dssad_eol_gui 导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_sn_format():
    """检查序列号格式"""
    test_sn = " XX1128000001Q0"
    print(f"测试序列号: '{test_sn}'")
    print(f"长度: {len(test_sn)} 字节")
    
    if len(test_sn) == 16:
        print("✅ 序列号长度正确")
    else:
        print("❌ 序列号长度错误")
        return False
    
    if test_sn[0] == ' ':
        print("✅ 第一个字节是空格")
    else:
        print("❌ 第一个字节不是空格")
        return False
    
    # 显示十六进制
    sn_bytes = test_sn.encode('ascii')
    hex_sn = ' '.join(f'{b:02X}' for b in sn_bytes)
    print(f"十六进制: {hex_sn}")
    
    # 显示分帧
    print("\n预期分帧:")
    print(f"第一帧: FF FE 00 10 01 00 {sn_bytes[0]:02X} {sn_bytes[1]:02X}")
    print(f"第二帧: {' '.join(f'{b:02X}' for b in sn_bytes[2:10])}")
    print(f"第三帧: {' '.join(f'{b:02X}' for b in sn_bytes[10:16])} AA AA")
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("DSSAD 序列号写入代码检查")
    print("=" * 50)
    
    success = True
    
    print("\n1. 检查模块导入...")
    if not check_imports():
        success = False
    
    print("\n2. 检查序列号格式...")
    if not check_sn_format():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 所有检查通过")
        print("\n修改总结:")
        print("1. 更新了 write_sn 方法，按照图片报文格式分帧发送")
        print("2. 第一帧: FF FE 00 10 01 00 + 序列号前2字节")
        print("3. 第二帧: 序列号第3-10字节")
        print("4. 第三帧: 序列号第11-16字节 + AA AA填充")
        print("5. 更新了GUI默认序列号为 ' XX11280000001Q'")
        print("6. 更新了配置文件中的默认序列号")
        print("7. 添加了配置加载到GUI中")
    else:
        print("❌ 检查失败")
    print("=" * 50)

if __name__ == "__main__":
    main()
