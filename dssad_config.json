{"can_settings": {"device_type": "USBCANFD-200U", "device_index": 0, "channel_index": 0, "baudrate": 500000, "request_id": "0x7D0", "response_id": "0x7D1", "use_canfd": true, "canfd_brs": true}, "eol_settings": {"command_timeout": 1.0, "multiframe_delay": 0.001, "retry_count": 3, "security_algorithm": "custom", "require_key_injection": false, "key_injection_extended": "D6BBE81FD85D44A7166700F634B99F7E", "key_injection_programming": "02D6BBE81FD85D44A71667F634B99F7E"}, "test_data": {"default_sn": " TEST_SN_123456", "default_ecu_hw_number": "1183883601", "default_im_part_number": "1196687701", "hw_version_mapping": {"0x00": "A0", "0x01": "A1", "0x10": "B0", "0x11": "B1", "0x20": "C0", "0x21": "C1"}}, "security_keys": {"note": "这些是示例密钥，实际使用时需要根据DSSAD规范配置", "xor_key_1": "0x5A5A5A5A", "xor_key_2": "0xA5A5A5A5", "shift_bits": 8}, "logging": {"level": "INFO", "save_logs": true, "log_directory": "logs", "max_log_files": 10}, "ui_settings": {"window_width": 1000, "window_height": 800, "font_size": 9, "theme": "default"}, "network_management": {"note": "网络管理帧配置 - 需要外部工具发送", "wakeup_frame": {"can_id": "0x480", "data": "80 00 00 00 00 00 00 00", "period_ms": 500}, "usage_mode_frame": {"can_id": "0x1F1", "data": "00 00 00 20 2C 00 00 00", "period_ms": 20, "description": "VehMd=FACTORY, UsgMd=Comfortable"}}, "test_sequences": {"basic_test": ["security_key_injection_both_levels", "security_access", "enter_eol_mode", "read_internal_hw_version", "read_sw_version", "read_ecu_hw_number", "read_sn", "read_manufacture_date", "read_im_part_number", "read_ig_level", "exit_eol_mode"], "production_test": ["security_key_injection_both_levels", "security_access", "enter_eol_mode", "confirm_secure_boot_enable_mcu", "confirm_secure_boot_enable_soc", "read_internal_hw_version", "write_ecu_hw_number", "write_sn", "write_manufacture_date", "write_im_part_number", "read_temperature", "clear_all_dtc", "exit_eol_mode"]}, "data_formats": {"sn_format": {"length": 16, "encoding": "ascii", "prefix": " ", "description": "第1字节固定为0x20，后15字节为追溯码"}, "manufacture_date_format": {"length": 3, "encoding": "bcd", "format": "YYMMDD", "description": "3字节BCD格式的生产日期"}, "ecu_hw_number_format": {"length": 5, "encoding": "bcd", "prefix": "11838836", "description": "前4字节固定，最后1字节为版本"}, "im_part_number_format": {"length": 5, "encoding": "bcd", "prefix": "11966877", "description": "前4字节固定，最后1字节为版本"}}}