# -*- coding: utf-8 -*-
"""
DSSAD EOL测试工具配置管理器
"""
import json
import os
from typing import Dict, Any, Optional

class DSSADConfig:
    """DSSAD配置管理器"""
    
    def __init__(self, config_file: str = "dssad_config.json"):
        self.config_file = config_file
        self.config: Dict[str, Any] = {}
        self.load_config()
        
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                return True
            else:
                print(f"配置文件 {self.config_file} 不存在，使用默认配置")
                self._create_default_config()
                return False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._create_default_config()
            return False
            
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
            
    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            "can_settings": {
                "device_type": "USBCANFD-200U",
                "device_index": 0,
                "channel_index": 0,
                "baudrate": 500000,
                "request_id": "0x7D0",
                "response_id": "0x7D1"
            },
            "eol_settings": {
                "command_timeout": 1.0,
                "multiframe_delay": 0.002,
                "retry_count": 3
            },
            "test_data": {
                "default_sn": " TEST_SN_123456",
                "default_ecu_hw_number": "1183883601",
                "default_im_part_number": "1196687701"
            }
        }
        
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path: str, value: Any) -> bool:
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        
        try:
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            config[keys[-1]] = value
            return True
        except Exception as e:
            print(f"设置配置值失败: {e}")
            return False
            
    def get_can_settings(self) -> Dict[str, Any]:
        """获取CAN设置"""
        return self.get("can_settings", {})
        
    def get_eol_settings(self) -> Dict[str, Any]:
        """获取EOL设置"""
        return self.get("eol_settings", {})
        
    def get_test_data(self) -> Dict[str, Any]:
        """获取测试数据"""
        return self.get("test_data", {})
        
    def get_security_keys(self) -> Dict[str, Any]:
        """获取安全密钥配置"""
        return self.get("security_keys", {})
        
    def get_request_id(self) -> int:
        """获取请求ID"""
        id_str = self.get("can_settings.request_id", "0x7D0")
        return int(id_str, 16) if isinstance(id_str, str) else id_str
        
    def get_response_id(self) -> int:
        """获取响应ID"""
        id_str = self.get("can_settings.response_id", "0x7D1")
        return int(id_str, 16) if isinstance(id_str, str) else id_str
        
    def get_command_timeout(self) -> float:
        """获取命令超时时间"""
        return self.get("eol_settings.command_timeout", 1.0)
        
    def get_hw_version_name(self, version_code: int) -> str:
        """获取硬件版本名称"""
        mapping = self.get("test_data.hw_version_mapping", {})
        key = f"0x{version_code:02X}"
        return mapping.get(key, f"Unknown(0x{version_code:02X})")
        
    def get_test_sequence(self, sequence_name: str) -> list:
        """获取测试序列"""
        return self.get(f"test_sequences.{sequence_name}", [])
        
    def get_data_format(self, format_name: str) -> Dict[str, Any]:
        """获取数据格式配置"""
        return self.get(f"data_formats.{format_name}", {})

# 全局配置实例
config = DSSADConfig()

def get_config() -> DSSADConfig:
    """获取全局配置实例"""
    return config

def reload_config() -> bool:
    """重新加载配置"""
    global config
    return config.load_config()

# 便捷函数
def get_can_request_id() -> int:
    """获取CAN请求ID"""
    return config.get_request_id()

def get_can_response_id() -> int:
    """获取CAN响应ID"""
    return config.get_response_id()

def get_default_sn() -> str:
    """获取默认序列号"""
    return config.get("test_data.default_sn", " TEST_SN_123456")

def get_default_device_type() -> str:
    """获取默认设备类型"""
    return config.get("can_settings.device_type", "USBCANFD-200U")

def get_command_timeout() -> float:
    """获取命令超时时间"""
    return config.get_command_timeout()

if __name__ == "__main__":
    # 测试配置管理器
    print("DSSAD配置管理器测试")
    print("=" * 40)
    
    cfg = DSSADConfig()
    
    print(f"CAN请求ID: 0x{cfg.get_request_id():03X}")
    print(f"CAN响应ID: 0x{cfg.get_response_id():03X}")
    print(f"命令超时: {cfg.get_command_timeout()}秒")
    print(f"默认设备类型: {get_default_device_type()}")
    print(f"默认序列号: {cfg.get('test_data.default_sn')}")
    
    # 测试硬件版本映射
    print("\n硬件版本映射:")
    for code in [0x00, 0x01, 0x10, 0x11, 0x20, 0x21]:
        name = cfg.get_hw_version_name(code)
        print(f"  0x{code:02X} -> {name}")
        
    # 测试测试序列
    basic_seq = cfg.get_test_sequence("basic_test")
    print(f"\n基本测试序列 ({len(basic_seq)}项):")
    for i, step in enumerate(basic_seq, 1):
        print(f"  {i}. {step}")
        
    print("\n配置管理器测试完成")
