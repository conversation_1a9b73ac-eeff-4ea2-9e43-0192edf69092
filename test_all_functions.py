# -*- coding: utf-8 -*-
"""
DSSAD EOL 全功能测试脚本
测试序列号、生产日期、IM零件号的读写功能
"""

import time
from datetime import datetime
from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
from dssad_config import DSSADConfig

def test_sn_functions(eol_tester):
    """测试序列号读写功能"""
    print("\n" + "="*60)
    print("测试序列号读写功能")
    print("="*60)
    
    # 测试序列号写入
    test_sn = " XX112800001Q000"  # 16字节
    print(f"写入测试序列号: '{test_sn}'")
    
    success = eol_tester.write_sn(test_sn.encode('ascii'))
    if success:
        print("✅ 序列号写入成功")
        
        # 读取序列号验证
        time.sleep(1)
        sn_data = eol_tester.read_sn()
        if sn_data:
            read_sn = sn_data.decode('ascii', errors='replace')
            print(f"读取到的序列号: '{read_sn}'")
            
            if read_sn == test_sn:
                print("✅ 序列号读写验证成功")
                return True
            else:
                print("❌ 序列号读写不一致")
                return False
        else:
            print("❌ 序列号读取失败")
            return False
    else:
        print("❌ 序列号写入失败")
        return False

def test_production_date_functions(eol_tester):
    """测试生产日期读写功能"""
    print("\n" + "="*60)
    print("测试生产日期读写功能")
    print("="*60)
    
    # 测试生产日期写入
    year = 2024
    month = 12
    day = 31
    
    date_data = eol_tester.format_production_date(year, month, day)
    hex_date = ' '.join(f'{b:02X}' for b in date_data)
    print(f"写入生产日期: {year}-{month:02d}-{day:02d} (BCD: {hex_date})")
    
    success = eol_tester.write_production_date(date_data)
    if success:
        print("✅ 生产日期写入成功")
        
        # 读取生产日期验证
        time.sleep(1)
        read_date_data = eol_tester.read_production_date()
        if read_date_data:
            read_date_tuple = eol_tester.parse_production_date(read_date_data)
            if read_date_tuple:
                read_year, read_month, read_day = read_date_tuple
                print(f"读取到的生产日期: {read_year}-{read_month:02d}-{read_day:02d}")
                
                if (read_year, read_month, read_day) == (year, month, day):
                    print("✅ 生产日期读写验证成功")
                    return True
                else:
                    print("❌ 生产日期读写不一致")
                    return False
            else:
                hex_read = ' '.join(f'{b:02X}' for b in read_date_data)
                print(f"生产日期解析失败，原始数据: {hex_read}")
                return False
        else:
            print("❌ 生产日期读取失败")
            return False
    else:
        print("❌ 生产日期写入失败")
        return False

def test_im_part_functions(eol_tester):
    """测试IM零件号读写功能"""
    print("\n" + "="*60)
    print("测试IM零件号读写功能")
    print("="*60)
    
    # 测试IM零件号写入
    base_number = "11966877"
    hw_version = 0x11  # B1版本
    
    part_data = eol_tester.format_im_part_number(base_number, hw_version)
    hex_part = ' '.join(f'{b:02X}' for b in part_data)
    print(f"写入IM零件号: {base_number} 版本: 0x{hw_version:02X} (BCD: {hex_part})")
    
    success = eol_tester.write_im_part_number(part_data)
    if success:
        print("✅ IM零件号写入成功")
        
        # 读取IM零件号验证
        time.sleep(1)
        read_part_data = eol_tester.read_im_part_number()
        if read_part_data:
            read_part_tuple = eol_tester.parse_im_part_number(read_part_data)
            if read_part_tuple:
                read_base, read_version = read_part_tuple
                print(f"读取到的IM零件号: {read_base} 版本: 0x{read_version:02X}")
                
                if read_base == base_number and read_version == hw_version:
                    print("✅ IM零件号读写验证成功")
                    return True
                else:
                    print("❌ IM零件号读写不一致")
                    return False
            else:
                hex_read = ' '.join(f'{b:02X}' for b in read_part_data)
                print(f"IM零件号解析失败，原始数据: {hex_read}")
                return False
        else:
            print("❌ IM零件号读取失败")
            return False
    else:
        print("❌ IM零件号写入失败")
        return False

def main():
    """主测试函数"""
    print("DSSAD EOL 全功能测试工具")
    print("测试序列号、生产日期、IM零件号的读写功能")
    print("请确保:")
    print("1. CAN设备已正确连接")
    print("2. DSSAD设备已上电并连接到CAN总线")
    print("3. 网络管理帧正在发送")
    print("4. 设备已进入EOL模式")
    
    input("\n按回车键开始测试...")
    
    try:
        # 连接CAN设备
        print("\n连接CAN设备...")
        can_manager = ZLGCANManager()
        success = can_manager.connect("USBCANFD-200U", 0, 0)
        if not success:
            print("❌ CAN设备连接失败")
            return
        
        print("✅ CAN设备连接成功")
        
        # 创建EOL测试器
        config = DSSADConfig()
        eol_tester = DSADEOLTester(can_manager, config.config)
        
        # 等待设备稳定
        print("等待设备稳定...")
        time.sleep(2)
        
        # 测试结果统计
        test_results = []
        
        # 测试序列号功能
        sn_result = test_sn_functions(eol_tester)
        test_results.append(("序列号读写", sn_result))
        
        # 测试生产日期功能
        date_result = test_production_date_functions(eol_tester)
        test_results.append(("生产日期读写", date_result))
        
        # 测试IM零件号功能
        im_result = test_im_part_functions(eol_tester)
        test_results.append(("IM零件号读写", im_result))
        
        # 显示测试结果汇总
        print("\n" + "="*60)
        print("测试结果汇总")
        print("="*60)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("\n" + "="*60)
        if all_passed:
            print("🎉 所有测试通过！")
        else:
            print("❌ 部分测试失败")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'can_manager' in locals():
                can_manager.disconnect()
                print("\nCAN设备已断开连接")
        except:
            pass

if __name__ == "__main__":
    main()
