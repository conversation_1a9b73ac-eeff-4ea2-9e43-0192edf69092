# DSSAD EOL测试工具

这是一个基于Python开发的DSSAD EOL（End of Line）测试工具，实现了完整的EOL测试协议，包括23个测试命令。

## 功能特性

### 支持的EOL测试命令
1. **进入/退出EOL模式** (0x81/0x82)
2. **读取内部硬件版本号** (0x14)
3. **电控单元硬件号读写** (0x83/0x84)
4. **软件版本号读取** (0x15)
5. **IG硬线电平检测** (0x80)
6. **序列号(SN)读写** (0x01/0x11)
7. **生产日期读写** (0x94/0x95)
8. **IM零件号读写** (0x05/0x1B)
9. **HSM确认** (0x90)
10. **主副电状态读取** (0x91)
11. **以太网口测试** (0x92)
12. **温度值读取** (0x93)
13. **DTC清除/读取** (0x52/0x51)
14. **安全访问** (0x97/0x98)
15. **安全启动使能确认** (0x99)
16. **安全访问密钥灌装** (0x20)

### 主要特点
- 完整的EOL协议实现
- 图形化用户界面(GUI)
- 命令行测试工具
- 多帧数据传输支持
- 实时日志记录
- 测试报告生成
- 数据格式转换(BCD, ASCII等)

## 文件结构

```
├── dssad_eol_tester.py     # EOL测试核心库
├── dssad_eol_gui.py        # GUI界面程序
├── test_dssad_eol.py       # 命令行测试工具
├── uds_toolkit_lib.py      # CAN通信基础库
├── zlgcan.py              # 周立功CAN设备驱动
├── kerneldlls/            # CAN设备驱动DLL文件
└── README_DSSAD_EOL.md    # 本说明文件
```

## 安装要求

### 硬件要求
- 周立功CAN设备 (USBCAN-I, USBCANFD-200U等)
- DSSAD测试设备
- CAN总线连接线

### 软件要求
- Python 3.7+
- tkinter (GUI界面)
- Windows操作系统 (驱动DLL要求)

### Python依赖包
```bash
# 基本依赖 (通常已内置)
tkinter
threading
ctypes
struct
datetime
```

## 使用方法

### 1. GUI界面使用

运行图形界面程序：
```bash
python dssad_eol_gui.py
```

#### 操作步骤：
1. **设备连接**
   - 选择CAN设备类型
   - 设置设备索引和通道索引
   - 点击"连接"按钮

2. **EOL测试**
   - 点击"安全访问"进行安全认证
   - 点击"进入EOL模式"激活测试模式
   - 使用各种测试按钮进行单项测试
   - 或点击"完整测试"运行全流程

3. **数据写入**
   - 在数据写入区域输入序列号和生产日期
   - 点击对应的写入按钮

4. **结果查看**
   - 查看实时日志了解测试过程
   - 生成测试报告保存结果

### 2. 命令行测试

运行命令行测试工具：
```bash
python test_dssad_eol.py
```

选择测试类型：
- 1: 基本通信测试 (读取各种信息)
- 2: 写入操作测试 (测试数据写入功能)
- 3: 完整测试流程 (包含读取和写入)

### 3. 编程接口使用

```python
from uds_toolkit_lib import ZLGCANManager
from dssad_eol_tester import DSSADEOLTester

# 创建CAN管理器
can_manager = ZLGCANManager()
can_manager.connect("USBCANFD-200U", 0, 0)

# 创建EOL测试器
eol_tester = DSSADEOLTester(can_manager)

# 执行测试
if eol_tester.security_access():
    if eol_tester.enter_eol_mode():
        # 读取硬件版本
        hw_version = eol_tester.read_internal_hw_version()
        print(f"硬件版本: 0x{hw_version:02X}")
        
        # 读取软件版本
        sw_version = eol_tester.read_sw_version()
        
        # 退出EOL模式
        eol_tester.exit_eol_mode()

# 断开连接
can_manager.disconnect()
```

## 配置说明

### CAN通信参数
- **请求ID**: 0x7D0
- **响应ID**: 0x7D1
- **波特率**: 500K
- **数据长度**: 8字节 (支持多帧传输)

### EOL命令格式
```
命令头: FF FE
参数长度: 2字节 (大端序)
命令ID: 1字节
子功能: 1字节 (0x00=请求, 0x02=回复)
参数: 可变长度
填充: 0xAA或0xCC (补齐到8字节)
```

### 响应格式
```
命令头: FF FE
参数长度: 2字节
命令ID: 1字节
子功能: 1字节 (0x02=回复)
结果码: 1字节 (0x00=成功, 0x01=失败, 0x02=安全访问未解锁, 0x03=EOL off)
数据: 可变长度
```

## 数据格式说明

### 序列号格式
- 长度: 16字节ASCII码
- 格式: 第1字节固定为0x20，后15字节为追溯码

### 生产日期格式
- 长度: 3字节BCD码
- 格式: YY MM DD (年月日)

### 电控单元硬件号格式
- 长度: 5字节BCD码
- 格式: 前4字节固定为11 83 88 36，最后1字节为版本号

### IM零件号格式
- 长度: 5字节BCD码
- 格式: 前4字节固定为11 96 68 77，最后1字节为版本号

## 故障排除

### 常见问题

1. **CAN设备连接失败**
   - 检查设备是否正确连接
   - 确认设备驱动已安装
   - 检查设备索引和通道索引是否正确

2. **安全访问失败**
   - 确认DSSAD设备已正确上电
   - 检查网络管理帧是否正在发送
   - 验证安全访问算法是否正确

3. **进入EOL模式失败**
   - 确保安全访问已成功
   - 检查DSSAD是否处于正确状态
   - 验证命令格式是否正确

4. **数据读取失败**
   - 确认已进入EOL模式
   - 检查命令超时设置
   - 验证响应数据格式

### 调试建议

1. **启用详细日志**
   - GUI界面会显示所有CAN通信日志
   - 检查发送和接收的原始数据

2. **使用命令行工具**
   - 先使用test_dssad_eol.py进行基本测试
   - 逐步验证各个功能模块

3. **检查硬件连接**
   - 确认CAN总线终端电阻
   - 检查波特率设置
   - 验证电源和地线连接

## 注意事项

1. **安全访问算法**
   - 当前使用简单的异或算法作为示例
   - 实际使用时需要实现正确的密钥计算算法

2. **数据写入操作**
   - 写入操作会永久修改DSSAD中的数据
   - 请确保数据格式正确后再执行写入

3. **测试环境**
   - 确保DSSAD处于测试模式
   - 避免在生产环境中进行测试

4. **兼容性**
   - 工具基于Windows平台开发
   - CAN设备驱动仅支持周立功设备

## 技术支持

如有问题或建议，请检查：
1. 日志文件中的详细错误信息
2. CAN通信原始数据
3. DSSAD设备状态和配置
4. 硬件连接和电源状态

## 版本历史

- v1.0: 初始版本，实现基本EOL测试功能
- 支持所有23个EOL命令
- 提供GUI和命令行界面
- 包含完整的测试流程
