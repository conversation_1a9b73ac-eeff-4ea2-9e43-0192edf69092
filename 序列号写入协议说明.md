# DSSAD 序列号写入协议实现说明

## 协议要求

根据提供的通信协议文档：

### 1. 序列号（SN号）写入
- **请求命令**: `FF FE 00 10 01 00 ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ ZZ`
- **回复命令**: `FF FE 00 01 01 02 XX`
- **ZZ**: 序列号，16字节ASCII码
- **XX**: 指令执行结果
  - `0x00` = 成功
  - `0x01` = 失败  
  - `0x03` = EOL off

### 2. 序列号格式要求
- 序列号要求16字节
- 由于追溯码只有15字节，所以序列号写入命令中第一个字节为空格（0x20）

## 实际报文分帧

根据图片中的CAN报文示例，请求命令需要分成3帧发送：

### 报文示例分析
```
第一帧: FF FE 00 10 01 00 20 58
第二帧: 58 58 31 31 32 38 30 30  
第三帧: 30 30 30 30 31 51 AA AA
```

对应的序列号为: `" XX112800001Q000"` (16字节)

### 分帧规则
1. **第一帧**: `FF FE 00 10 01 00` + 序列号前2字节
2. **第二帧**: 序列号第3-10字节 (8字节)
3. **第三帧**: 序列号第11-16字节 + 填充`AA AA`

## 代码实现

### 修改的write_sn方法

```python
def write_sn(self, sn_data: bytes) -> bool:
    """写入序列号 - 命令ID: 0x01 (多帧发送)"""
    
    # 验证序列号长度和格式
    if len(sn_data) != 16:
        return False
    if sn_data[0] != 0x20:  # 第一个字节必须是空格
        return False
    
    # 第一帧：FF FE 00 10 01 00 + 序列号前2字节
    frame1 = bytearray([0xFF, 0xFE, 0x00, 0x10, 0x01, 0x00])
    frame1.extend(sn_data[0:2])
    
    # 第二帧：序列号第3-10字节
    frame2 = bytearray(sn_data[2:10])
    
    # 第三帧：序列号第11-16字节 + 填充AA
    frame3 = bytearray(sn_data[10:16])
    frame3.extend([0xAA, 0xAA])
    
    # 依次发送三帧
    # ... 发送逻辑和响应处理
```

### GUI界面更新

- 默认序列号更新为: `" XX11280000001Q"`
- 符合16字节要求，第一个字节为空格

## 测试验证

### 测试脚本
创建了 `test_sn_write.py` 用于验证序列号写入功能：

1. 连接CAN设备
2. 创建EOL测试器
3. 发送测试序列号
4. 验证写入结果

### 测试序列号
- 测试序列号: `" XX112800001Q000"`
- 十六进制: `20 58 58 31 31 32 38 30 30 30 30 31 51 30 30 30`

### 预期分帧
```
第一帧: FF FE 00 10 01 00 20 58
第二帧: 58 31 31 32 38 30 30 30
第三帧: 30 31 51 30 30 30 AA AA
```

## 使用说明

### 1. GUI界面使用
1. 启动 `dssad_eol_gui.py`
2. 连接CAN设备
3. 启动网络管理
4. 在序列号输入框中输入16字节序列号
5. 点击"写入SN"按钮

### 2. 编程接口使用
```python
from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
from dssad_config import DSSADConfig

# 连接设备
can_manager = ZLGCANManager()
can_manager.connect("USBCANFD-200U", 0, 0)

# 创建测试器
config = DSSADConfig()
eol_tester = DSADEOLTester(can_manager, config.config)

# 写入序列号
sn = " XX112800001Q000"
success = eol_tester.write_sn(sn.encode('ascii'))
```

## 注意事项

1. **序列号格式**: 必须是16字节，第一个字节为空格(0x20)
2. **EOL模式**: 写入前需要进入EOL模式
3. **网络管理**: 需要外部工具发送网络管理帧
4. **错误处理**: 检查响应中的错误码
5. **数据备份**: 写入前建议备份原始序列号

## 错误码说明

响应格式: `FF FE 00 01 01 02 XX`

- `XX = 0x00`: 写入成功
- `XX = 0x01`: 写入失败
- `XX = 0x03`: EOL模式未激活

## 相关文件

- `uds_toolkit_lib_fixed.py`: 核心通信库，包含修改后的write_sn方法
- `dssad_eol_gui.py`: GUI界面，更新了默认序列号
- `test_sn_write.py`: 测试脚本
- `dssad_config.py`: 配置管理
- `dssad_config.json`: 配置文件
