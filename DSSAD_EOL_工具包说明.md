# DSSAD EOL测试工具包

## 概述

这是一个完整的DSSAD EOL（End of Line）测试工具包，基于您提供的EOL测试协议和CAN报文数据开发。工具包实现了所有23个EOL测试命令，提供了图形界面、命令行工具和编程接口。

## 文件清单

### 核心文件
- `dssad_eol_tester.py` - EOL测试核心库，实现所有测试命令
- `dssad_eol_gui.py` - 图形用户界面程序
- `dssad_config.py` - 配置管理器
- `dssad_config.json` - 配置文件

### 测试工具
- `test_dssad_eol.py` - 命令行测试工具
- `example_usage.py` - 使用示例和演示代码

### 基础库
- `uds_toolkit_lib.py` - CAN通信基础库
- `zlgcan.py` - 周立功CAN设备驱动接口

### 启动脚本
- `start_dssad_eol.bat` - Windows批处理启动脚本

### 文档
- `README_DSSAD_EOL.md` - 详细使用说明
- `DSSAD_EOL_工具包说明.md` - 本文件

## 快速开始

### 1. 环境准备
```bash
# 确保Python 3.7+已安装
python --version

# 检查必要的库（通常已内置）
python -c "import tkinter, threading, ctypes, json"
```

### 2. 硬件连接
- 连接周立功CAN设备到计算机
- 将DSSAD设备连接到CAN总线
- 确保DSSAD设备已上电

### 3. 配置设置
编辑 `dssad_config.json` 文件，设置正确的设备参数：
```json
{
    "can_settings": {
        "device_type": "USBCANFD-200U",  // 根据您的设备修改
        "device_index": 0,
        "channel_index": 0
    }
}
```

### 4. 运行测试

#### 方法1: 使用启动脚本（推荐）
```bash
start_dssad_eol.bat
```

#### 方法2: 直接运行GUI
```bash
python dssad_eol_gui.py
```

#### 方法3: 命令行测试
```bash
python test_dssad_eol.py
```

#### 方法4: 使用示例代码
```bash
python example_usage.py
```

## 主要功能

### 支持的EOL命令
1. **安全访问** (0x97/0x98) - 种子密钥认证
2. **EOL模式控制** (0x81/0x82) - 进入/退出EOL模式
3. **硬件信息读取** (0x14) - 内部硬件版本号
4. **软件版本读取** (0x15) - 软件版本号
5. **电控单元硬件号** (0x83/0x84) - 读写硬件号
6. **序列号管理** (0x01/0x11) - 读写序列号
7. **生产日期管理** (0x94/0x95) - 读写生产日期
8. **IM零件号管理** (0x05/0x1B) - 读写零件号
9. **安全启动检查** (0x99) - MCU/SOC安全启动状态
10. **环境检测** (0x80/0x93) - IG电平/温度检测
11. **DTC管理** (0x51/0x52) - 读取/清除故障码

### 特色功能
- **多种界面**: GUI、命令行、编程接口
- **配置管理**: JSON配置文件，灵活设置参数
- **数据格式转换**: BCD、ASCII、HEX自动转换
- **实时日志**: 详细的通信日志记录
- **测试报告**: 自动生成测试报告
- **错误处理**: 完善的异常处理和错误提示

## 使用场景

### 1. 生产线测试
使用GUI界面进行批量测试：
- 连接设备
- 运行完整测试流程
- 查看测试结果
- 生成测试报告

### 2. 开发调试
使用命令行工具进行快速测试：
- 验证通信连接
- 测试单个命令
- 调试协议问题

### 3. 自动化集成
使用编程接口集成到自动化系统：
```python
from dssad_eol_tester import DSSADEOLTester
from uds_toolkit_lib import ZLGCANManager

# 创建测试器
can_manager = ZLGCANManager()
can_manager.connect("USBCANFD-200U", 0, 0)
tester = DSSADEOLTester(can_manager)

# 执行测试
if tester.run_complete_eol_test():
    print("测试通过")
else:
    print("测试失败")
```

## 配置说明

### CAN通信配置
```json
"can_settings": {
    "device_type": "USBCANFD-200U",    // CAN设备类型
    "device_index": 0,                 // 设备索引
    "channel_index": 0,                // 通道索引
    "request_id": "0x7D0",            // EOL请求ID
    "response_id": "0x7D1"            // EOL响应ID
}
```

### 安全访问配置
```json
"security_keys": {
    "xor_key_1": "0x5A5A5A5A",       // 第一个异或密钥
    "xor_key_2": "0xA5A5A5A5",       // 第二个异或密钥
    "shift_bits": 8                   // 位移位数
}
```

### 测试数据配置
```json
"test_data": {
    "default_sn": " TEST_SN_123456",           // 默认序列号
    "default_ecu_hw_number": "1183883601",     // 默认ECU硬件号
    "default_im_part_number": "1196687701"     // 默认IM零件号
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查CAN设备连接
   - 确认设备驱动已安装
   - 验证设备索引和通道索引

2. **安全访问失败**
   - 确认DSSAD设备状态
   - 检查网络管理帧
   - 验证密钥算法

3. **命令超时**
   - 增加超时时间设置
   - 检查CAN总线连接
   - 确认DSSAD响应

4. **数据格式错误**
   - 检查数据长度
   - 验证BCD格式
   - 确认ASCII编码

### 调试技巧

1. **启用详细日志**
   - GUI界面显示所有通信日志
   - 检查发送和接收的原始数据

2. **使用测试工具**
   - 先运行 `test_dssad_eol.py` 验证基本功能
   - 使用 `example_usage.py` 学习API使用

3. **检查配置文件**
   - 验证 `dssad_config.json` 中的参数
   - 确认CAN ID和超时设置

## 扩展开发

### 添加新的EOL命令
1. 在 `EOLCommand` 类中添加命令ID
2. 在 `DSSADEOLTester` 类中实现命令方法
3. 在GUI中添加对应的按钮和处理

### 修改密钥算法
1. 编辑 `_calculate_key` 方法
2. 或在配置文件中添加新的算法参数
3. 测试验证算法正确性

### 自定义测试序列
1. 在配置文件的 `test_sequences` 中添加新序列
2. 使用 `get_test_sequence` 方法获取序列
3. 按序列执行测试命令

## 技术支持

### 日志分析
- 查看GUI界面的实时日志
- 保存日志文件进行离线分析
- 检查CAN通信的原始数据

### 问题报告
报告问题时请提供：
1. 详细的错误信息
2. 完整的日志文件
3. 硬件配置信息
4. 复现步骤

### 版本更新
- 定期检查工具包更新
- 备份配置文件和测试数据
- 测试新版本的兼容性

## 注意事项

1. **安全警告**
   - 写入操作会永久修改DSSAD数据
   - 请在测试环境中验证后再用于生产

2. **兼容性**
   - 工具基于Windows平台开发
   - 需要周立功CAN设备支持

3. **性能优化**
   - 合理设置超时时间
   - 避免频繁的连接断开操作

4. **数据备份**
   - 写入前备份原始数据
   - 保存重要的测试结果

---

**版本**: v1.0  
**更新日期**: 2024年  
**开发语言**: Python 3.7+  
**支持平台**: Windows  
**CAN设备**: 周立功USBCAN系列
