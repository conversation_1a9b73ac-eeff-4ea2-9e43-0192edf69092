# -*- coding: utf-8 -*-
"""
测试序列号写入功能
验证按照图片中的报文格式进行分帧发送
"""

import time
from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
from dssad_config import DSSADConfig

def test_sn_write():
    """测试序列号写入功能"""
    print("=" * 50)
    print("DSSAD 序列号写入测试")
    print("=" * 50)
    
    # 加载配置
    config = DSSADConfig()
    
    # 创建CAN管理器
    can_manager = ZLGCANManager()
    
    try:
        # 连接CAN设备
        print("正在连接CAN设备...")
        success = can_manager.connect("USBCANFD-200U", 0, 0)
        if not success:
            print("❌ CAN设备连接失败")
            return False
        
        print("✅ CAN设备连接成功")
        
        # 创建EOL测试器
        eol_tester = DSADEOLTester(can_manager, config.config)
        
        # 测试序列号
        test_sn = " XX112800001Q000"  # 16字节，根据图片示例
        print(f"测试序列号: '{test_sn}' (长度: {len(test_sn)})")
        
        # 显示序列号的十六进制表示
        sn_bytes = test_sn.encode('ascii')
        hex_sn = ' '.join(f'{b:02X}' for b in sn_bytes)
        print(f"序列号十六进制: {hex_sn}")
        
        print("\n根据协议，序列号将分3帧发送:")
        print(f"第一帧: FF FE 00 10 01 00 {sn_bytes[0]:02X} {sn_bytes[1]:02X}")
        print(f"第二帧: {' '.join(f'{b:02X}' for b in sn_bytes[2:10])}")
        print(f"第三帧: {' '.join(f'{b:02X}' for b in sn_bytes[10:16])} AA AA")
        
        # 执行写入测试
        print("\n开始写入序列号...")
        success = eol_tester.write_sn(sn_bytes)
        
        if success:
            print("🎉 序列号写入测试成功!")
            
            # 尝试读取验证
            print("\n验证写入结果...")
            time.sleep(1)  # 等待一秒
            read_sn = eol_tester.read_sn()
            if read_sn:
                read_sn_str = read_sn.decode('ascii', errors='replace')
                print(f"读取到的序列号: '{read_sn_str}'")
                if read_sn_str == test_sn:
                    print("✅ 序列号写入验证成功!")
                else:
                    print("⚠️ 序列号写入验证失败，读取值与写入值不匹配")
            else:
                print("❌ 无法读取序列号进行验证")
        else:
            print("❌ 序列号写入测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False
    finally:
        # 断开连接
        can_manager.disconnect()
        print("\nCAN设备已断开连接")
    
    return success

def main():
    """主函数"""
    print("DSSAD 序列号写入功能测试工具")
    print("请确保:")
    print("1. CAN设备已正确连接")
    print("2. DSSAD设备已上电并连接到CAN总线")
    print("3. 网络管理帧正在发送")
    print()
    
    input("按回车键开始测试...")
    
    success = test_sn_write()
    
    print("\n" + "=" * 50)
    if success:
        print("测试完成: 成功")
    else:
        print("测试完成: 失败")
    print("=" * 50)
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
