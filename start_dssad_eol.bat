@echo off
echo ========================================
echo DSSAD EOL测试工具启动器
echo ========================================
echo.
echo 请选择要运行的程序:
echo 1. GUI界面程序 (推荐)
echo 2. 命令行测试工具
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 正在启动GUI界面程序...
    python dssad_eol_gui.py
) else if "%choice%"=="2" (
    echo.
    echo 正在启动命令行测试工具...
    python test_dssad_eol.py
) else if "%choice%"=="3" (
    echo 退出程序
    exit
) else (
    echo 无效选择，请重新运行
    pause
)

pause
