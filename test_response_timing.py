# -*- coding: utf-8 -*-
"""
测试响应等待逻辑修复
验证30-50毫秒响应时间的正确处理
"""

import time
from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
from dssad_config import DSSADConfig

def test_sn_write_response(eol_tester):
    """测试序列号写入响应等待"""
    print("\n" + "="*60)
    print("测试序列号写入响应等待")
    print("="*60)
    
    # 测试序列号写入
    test_sn = " XX112800001Q000"  # 16字节
    print(f"准备写入序列号: '{test_sn}'")
    
    print("预期响应: FF FE 00 01 01 02 00 AA")
    print("响应时间: 30-50毫秒")
    
    print("\n开始写入序列号...")
    start_time = time.time()
    success = eol_tester.write_sn(test_sn.encode('ascii'))
    end_time = time.time()
    
    print(f"写入耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if success:
        print("✅ 序列号写入成功")
        return True
    else:
        print("❌ 序列号写入失败")
        return False

def test_production_date_response(eol_tester):
    """测试生产日期写入响应等待"""
    print("\n" + "="*60)
    print("测试生产日期写入响应等待")
    print("="*60)
    
    # 测试生产日期
    year = 2025
    month = 7
    day = 31
    
    print(f"准备写入生产日期: {year}-{month:02d}-{day:02d}")
    
    # 格式化为BCD
    date_data = eol_tester.format_production_date(year, month, day)
    hex_date = ' '.join(f'{b:02X}' for b in date_data)
    print(f"BCD格式: {hex_date}")
    
    print("预期响应: FF FE 00 01 94 02 00 AA")
    print("响应时间: 30-50毫秒")
    
    print("\n开始写入生产日期...")
    start_time = time.time()
    success = eol_tester.write_production_date(date_data)
    end_time = time.time()
    
    print(f"写入耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if success:
        print("✅ 生产日期写入成功")
        return True
    else:
        print("❌ 生产日期写入失败")
        return False

def test_im_part_response(eol_tester):
    """测试IM零件号写入响应等待"""
    print("\n" + "="*60)
    print("测试IM零件号写入响应等待")
    print("="*60)
    
    # 测试IM零件号
    base_number = "11966877"
    hw_version = 0x11  # B1版本
    
    print(f"准备写入IM零件号: {base_number}, 硬件版本: 0x{hw_version:02X}")
    
    # 格式化为BCD
    part_data = eol_tester.format_im_part_number(base_number, hw_version)
    hex_part = ' '.join(f'{b:02X}' for b in part_data)
    print(f"BCD格式: {hex_part}")
    
    print("预期响应: FF FE 00 01 05 02 00 AA")
    print("响应时间: 30-50毫秒")
    
    print("\n开始写入IM零件号...")
    start_time = time.time()
    success = eol_tester.write_im_part_number(part_data)
    end_time = time.time()
    
    print(f"写入耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if success:
        print("✅ IM零件号写入成功")
        return True
    else:
        print("❌ IM零件号写入失败")
        return False

def test_sn_read_response(eol_tester):
    """测试序列号读取响应等待"""
    print("\n" + "="*60)
    print("测试序列号读取响应等待")
    print("="*60)
    
    print("预期响应:")
    print("第一帧: FF FE 00 11 11 02 00 20")
    print("第二帧: 58 58 31 31 32 38 30 30")
    print("第三帧: 30 30 31 51 30 30 30 AA")
    print("响应时间: 30-50毫秒")
    
    print("\n开始读取序列号...")
    start_time = time.time()
    sn_data = eol_tester.read_sn()
    end_time = time.time()
    
    print(f"读取耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if sn_data:
        print("✅ 序列号读取成功")
        
        # 显示读取到的数据
        hex_read = ' '.join(f'{b:02X}' for b in sn_data)
        print(f"读取到的十六进制: {hex_read}")
        
        try:
            read_sn = sn_data.decode('ascii', errors='replace')
            print(f"读取到的序列号: '{read_sn}'")
        except Exception as e:
            print(f"序列号解码失败: {e}")
        
        return True
    else:
        print("❌ 序列号读取失败")
        return False

def main():
    """主测试函数"""
    print("DSSAD EOL 响应等待逻辑测试")
    print("验证30-50毫秒响应时间的正确处理")
    
    print("\n请确保:")
    print("1. CAN设备已正确连接")
    print("2. DSSAD设备已上电并连接到CAN总线")
    print("3. 网络管理帧正在发送")
    print("4. 设备已进入EOL模式")
    print("5. 监控CAN报文，观察响应时间")
    
    input("\n按回车键开始测试...")
    
    try:
        # 连接CAN设备
        print("\n连接CAN设备...")
        can_manager = ZLGCANManager()
        success = can_manager.connect("USBCANFD-200U", 0, 0)
        if not success:
            print("❌ CAN设备连接失败")
            return
        
        print("✅ CAN设备连接成功")
        
        # 创建EOL测试器
        config = DSSADConfig()
        eol_tester = DSADEOLTester(can_manager, config.config)
        
        # 等待设备稳定
        print("等待设备稳定...")
        time.sleep(2)
        
        # 测试结果统计
        test_results = []
        
        print("\n🔍 测试响应等待逻辑，观察是否能正确接收30-50毫秒的响应")
        
        # 测试序列号读取响应
        sn_read_result = test_sn_read_response(eol_tester)
        test_results.append(("序列号读取响应", sn_read_result))
        
        time.sleep(1)
        
        # 测试序列号写入响应
        sn_write_result = test_sn_write_response(eol_tester)
        test_results.append(("序列号写入响应", sn_write_result))
        
        time.sleep(1)
        
        # 测试生产日期写入响应
        date_result = test_production_date_response(eol_tester)
        test_results.append(("生产日期写入响应", date_result))
        
        time.sleep(1)
        
        # 测试IM零件号写入响应
        im_result = test_im_part_response(eol_tester)
        test_results.append(("IM零件号写入响应", im_result))
        
        # 显示测试结果汇总
        print("\n" + "="*60)
        print("测试结果汇总")
        print("="*60)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("\n" + "="*60)
        if all_passed:
            print("🎉 所有测试通过！响应等待逻辑修复成功！")
            print("设备响应时间在30-50毫秒范围内，代码能正确处理")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'can_manager' in locals():
                can_manager.disconnect()
                print("\nCAN设备已断开连接")
        except:
            pass

if __name__ == "__main__":
    main()
