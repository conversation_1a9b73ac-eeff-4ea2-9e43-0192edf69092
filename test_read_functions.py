# -*- coding: utf-8 -*-
"""
测试读取功能的分帧响应处理
验证生产日期和IM零件号读取的分帧响应解析
"""

import time
from uds_toolkit_lib_fixed import ZLGCANManager, DSADEOLTester
from dssad_config import DSSADConfig

def test_production_date_read(eol_tester):
    """测试生产日期读取分帧响应"""
    print("\n" + "="*60)
    print("测试生产日期读取分帧响应")
    print("="*60)
    
    print("预期的分帧响应:")
    print("第一帧: FF FE 00 04 95 02 00 25 (8字节)")
    print("第二帧: 07 31 AA AA AA AA AA AA (8字节)")
    print("解析结果: 25 07 31 (3字节BCD)")
    
    print("\n开始读取生产日期...")
    start_time = time.time()
    date_data = eol_tester.read_production_date()
    end_time = time.time()
    
    print(f"读取耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if date_data:
        print("✅ 生产日期读取成功")
        
        # 显示读取到的数据
        hex_read = ' '.join(f'{b:02X}' for b in date_data)
        print(f"读取到的BCD: {hex_read}")
        
        # 解析日期
        date_tuple = eol_tester.parse_production_date(date_data)
        if date_tuple:
            year, month, day = date_tuple
            print(f"解析后的日期: {year}-{month:02d}-{day:02d}")
        else:
            print("⚠️ 日期解析失败")
        
        return True
    else:
        print("❌ 生产日期读取失败")
        return False

def test_im_part_read(eol_tester):
    """测试IM零件号读取分帧响应"""
    print("\n" + "="*60)
    print("测试IM零件号读取分帧响应")
    print("="*60)
    
    print("预期的分帧响应:")
    print("第一帧: FF FE 00 06 1B 02 00 [1字节] (8字节)")
    print("第二帧: [4字节] AA AA AA AA (8字节)")
    print("解析结果: [5字节BCD]")
    
    print("\n开始读取IM零件号...")
    start_time = time.time()
    part_data = eol_tester.read_im_part_number()
    end_time = time.time()
    
    print(f"读取耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if part_data:
        print("✅ IM零件号读取成功")
        
        # 显示读取到的数据
        hex_read = ' '.join(f'{b:02X}' for b in part_data)
        print(f"读取到的BCD: {hex_read}")
        
        # 解析零件号
        part_tuple = eol_tester.parse_im_part_number(part_data)
        if part_tuple:
            base_number, hw_version = part_tuple
            print(f"解析后的零件号: {base_number}, 版本: 0x{hw_version:02X}")
        else:
            print("⚠️ 零件号解析失败")
        
        return True
    else:
        print("❌ IM零件号读取失败")
        return False

def test_sn_read_multiframe(eol_tester):
    """测试序列号读取多帧响应"""
    print("\n" + "="*60)
    print("测试序列号读取多帧响应")
    print("="*60)
    
    print("预期的分帧响应:")
    print("第一帧: FF FE 00 11 11 02 00 20 (8字节)")
    print("第二帧: 58 58 31 31 32 38 30 30 (8字节)")
    print("第三帧: 30 30 31 51 30 30 30 AA (8字节)")
    print("解析结果: 16字节ASCII序列号")
    
    print("\n开始读取序列号...")
    start_time = time.time()
    sn_data = eol_tester.read_sn()
    end_time = time.time()
    
    print(f"读取耗时: {(end_time - start_time)*1000:.1f}毫秒")
    
    if sn_data:
        print("✅ 序列号读取成功")
        
        # 显示读取到的数据
        hex_read = ' '.join(f'{b:02X}' for b in sn_data)
        print(f"读取到的十六进制: {hex_read}")
        
        try:
            read_sn = sn_data.decode('ascii', errors='replace')
            print(f"读取到的序列号: '{read_sn}'")
        except Exception as e:
            print(f"序列号解码失败: {e}")
        
        return True
    else:
        print("❌ 序列号读取失败")
        return False

def test_write_then_read_cycle(eol_tester):
    """测试写入后读取的完整周期"""
    print("\n" + "="*60)
    print("测试写入后读取的完整周期")
    print("="*60)
    
    # 测试生产日期写入读取周期
    print("\n1. 生产日期写入读取周期")
    year, month, day = 2025, 7, 31
    date_data = eol_tester.format_production_date(year, month, day)
    
    print(f"写入日期: {year}-{month:02d}-{day:02d}")
    write_success = eol_tester.write_production_date(date_data)
    
    if write_success:
        print("✅ 生产日期写入成功")
        time.sleep(0.5)  # 等待设备处理
        
        read_data = eol_tester.read_production_date()
        if read_data:
            print("✅ 生产日期读取成功")
            date_tuple = eol_tester.parse_production_date(read_data)
            if date_tuple:
                read_year, read_month, read_day = date_tuple
                print(f"读取日期: {read_year}-{read_month:02d}-{read_day:02d}")
                if (read_year, read_month, read_day) == (year, month, day):
                    print("🎉 生产日期写入读取验证成功！")
                else:
                    print("⚠️ 生产日期写入读取不一致")
        else:
            print("❌ 生产日期读取失败")
    else:
        print("❌ 生产日期写入失败")
    
    time.sleep(1)
    
    # 测试IM零件号写入读取周期
    print("\n2. IM零件号写入读取周期")
    base_number = "11966877"
    hw_version = 0x11
    part_data = eol_tester.format_im_part_number(base_number, hw_version)
    
    print(f"写入零件号: {base_number}, 版本: 0x{hw_version:02X}")
    write_success = eol_tester.write_im_part_number(part_data)
    
    if write_success:
        print("✅ IM零件号写入成功")
        time.sleep(0.5)  # 等待设备处理
        
        read_data = eol_tester.read_im_part_number()
        if read_data:
            print("✅ IM零件号读取成功")
            part_tuple = eol_tester.parse_im_part_number(read_data)
            if part_tuple:
                read_base, read_version = part_tuple
                print(f"读取零件号: {read_base}, 版本: 0x{read_version:02X}")
                if read_base == base_number and read_version == hw_version:
                    print("🎉 IM零件号写入读取验证成功！")
                else:
                    print("⚠️ IM零件号写入读取不一致")
        else:
            print("❌ IM零件号读取失败")
    else:
        print("❌ IM零件号写入失败")

def main():
    """主测试函数"""
    print("DSSAD EOL 读取功能分帧响应测试")
    print("验证生产日期和IM零件号读取的分帧响应解析")
    
    print("\n请确保:")
    print("1. CAN设备已正确连接")
    print("2. DSSAD设备已上电并连接到CAN总线")
    print("3. 网络管理帧正在发送")
    print("4. 设备已进入EOL模式")
    print("5. 监控CAN报文，观察分帧响应")
    
    input("\n按回车键开始测试...")
    
    try:
        # 连接CAN设备
        print("\n连接CAN设备...")
        can_manager = ZLGCANManager()
        success = can_manager.connect("USBCANFD-200U", 0, 0)
        if not success:
            print("❌ CAN设备连接失败")
            return
        
        print("✅ CAN设备连接成功")
        
        # 创建EOL测试器
        config = DSSADConfig()
        eol_tester = DSADEOLTester(can_manager, config.config)
        
        # 等待设备稳定
        print("等待设备稳定...")
        time.sleep(2)
        
        # 测试结果统计
        test_results = []
        
        print("\n🔍 测试读取功能的分帧响应处理")
        
        # 测试序列号读取
        sn_read_result = test_sn_read_multiframe(eol_tester)
        test_results.append(("序列号读取", sn_read_result))
        
        time.sleep(1)
        
        # 测试生产日期读取
        date_read_result = test_production_date_read(eol_tester)
        test_results.append(("生产日期读取", date_read_result))
        
        time.sleep(1)
        
        # 测试IM零件号读取
        im_read_result = test_im_part_read(eol_tester)
        test_results.append(("IM零件号读取", im_read_result))
        
        time.sleep(1)
        
        # 测试完整的写入读取周期
        test_write_then_read_cycle(eol_tester)
        
        # 显示测试结果汇总
        print("\n" + "="*60)
        print("测试结果汇总")
        print("="*60)
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if not result:
                all_passed = False
        
        print("\n" + "="*60)
        if all_passed:
            print("🎉 所有读取测试通过！分帧响应处理修复成功！")
            print("设备的分帧响应能够正确解析")
        else:
            print("❌ 部分测试失败，需要进一步调试")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if 'can_manager' in locals():
                can_manager.disconnect()
                print("\nCAN设备已断开连接")
        except:
            pass

if __name__ == "__main__":
    main()
